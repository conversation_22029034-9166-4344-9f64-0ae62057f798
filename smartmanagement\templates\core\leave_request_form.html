{% extends 'base.html' %}

{% block title %}Apply for Leave - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }
    
    .form-header {
        background: var(--gradient-bg);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .leave-balance-info {
        background: #f8fafc;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--success-color);
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
    }
    
    .half-day-section {
        background: #fff7ed;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #fed7aa;
    }
    
    .date-info {
        background: #eff6ff;
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: #1e40af;
    }
    
    .btn-submit {
        background: var(--gradient-bg);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 50px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 70, 229, 0.4);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <h2><i class="fas fa-calendar-plus me-2"></i>Apply for Leave</h2>
                    <p class="mb-0">Submit your leave request for approval</p>
                </div>
                
                <!-- Leave Balance Information -->
                <div class="leave-balance-info">
                    <h5><i class="fas fa-info-circle me-2"></i>Your Leave Balances</h5>
                    <div class="row" id="leaveBalances">
                        <!-- Leave balances will be loaded here via AJAX -->
                        <div class="col-12">
                            <p class="mb-0">Loading leave balances...</p>
                        </div>
                    </div>
                </div>

                <!-- Holiday Information -->
                <div class="holiday-info" id="holidayInfo" style="display: none;">
                    <h5><i class="fas fa-calendar-alt me-2"></i>Holidays in Selected Period</h5>
                    <div id="holidayList">
                        <!-- Holidays will be loaded here via AJAX -->
                    </div>
                    <small class="text-muted">Note: Holidays are automatically excluded from leave calculations</small>
                </div>
                
                <!-- Leave Request Form -->
                <form method="post" id="leaveRequestForm">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.leave_type.id_for_label }}" class="form-label">
                                    Leave Type <span class="text-danger">*</span>
                                </label>
                                {{ form.leave_type }}
                                {% if form.leave_type.errors %}
                                    <div class="text-danger mt-1">{{ form.leave_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">
                                    Emergency Contact
                                </label>
                                {{ form.emergency_contact }}
                                {% if form.emergency_contact.errors %}
                                    <div class="text-danger mt-1">{{ form.emergency_contact.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Half Day Option -->
                    <div class="form-group">
                        <div class="form-check">
                            {{ form.is_half_day }}
                            <label class="form-check-label" for="{{ form.is_half_day.id_for_label }}">
                                This is a half-day leave
                            </label>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                    Start Date <span class="text-danger">*</span>
                                </label>
                                {{ form.start_date }}
                                {% if form.start_date.errors %}
                                    <div class="text-danger mt-1">{{ form.start_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                    End Date <span class="text-danger">*</span>
                                </label>
                                {{ form.end_date }}
                                {% if form.end_date.errors %}
                                    <div class="text-danger mt-1">{{ form.end_date.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Half Day Period (hidden by default) -->
                    <div class="half-day-section" id="halfDaySection" style="display: none;">
                        <div class="form-group">
                            <label for="{{ form.half_day_period.id_for_label }}" class="form-label">
                                Half Day Period <span class="text-danger">*</span>
                            </label>
                            {{ form.half_day_period }}
                            {% if form.half_day_period.errors %}
                                <div class="text-danger mt-1">{{ form.half_day_period.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Date Calculation Info -->
                    <div class="date-info" id="dateInfo" style="display: none;">
                        <i class="fas fa-calculator me-2"></i>
                        <span id="dateCalculation">Select dates to see calculation</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">
                            Reason for Leave <span class="text-danger">*</span>
                        </label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <div class="text-danger mt-1">{{ form.reason.errors.0 }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-submit me-3">
                            <i class="fas fa-paper-plane me-2"></i>Submit Leave Request
                        </button>
                        <a href="{% url 'core:dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const halfDayCheck = document.getElementById('halfDayCheck');
    const halfDaySection = document.getElementById('halfDaySection');
    const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');
    const leaveTypeSelect = document.getElementById('{{ form.leave_type.id_for_label }}');
    const dateInfo = document.getElementById('dateInfo');
    const dateCalculation = document.getElementById('dateCalculation');
    
    // Load leave balances
    loadLeaveBalances();

    // Half day toggle
    halfDayCheck.addEventListener('change', function() {
        if (this.checked) {
            halfDaySection.style.display = 'block';
            endDateInput.value = startDateInput.value;
            endDateInput.disabled = true;
        } else {
            halfDaySection.style.display = 'none';
            endDateInput.disabled = false;
        }
        calculateLeaveDays();
        loadHolidays();
    });
    
    // Date change handlers
    startDateInput.addEventListener('change', function() {
        if (halfDayCheck.checked) {
            endDateInput.value = this.value;
        }
        calculateLeaveDays();
        loadHolidays();
    });

    endDateInput.addEventListener('change', function() {
        calculateLeaveDays();
        loadHolidays();
    });
    leaveTypeSelect.addEventListener('change', loadLeaveBalances);
    
    function loadLeaveBalances() {
        fetch('{% url "core:check_leave_balance" %}')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('leaveBalances');
                if (data.balances && data.balances.length > 0) {
                    container.innerHTML = data.balances.map(balance => `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between">
                                <span>${balance.leave_type}</span>
                                <strong>${balance.remaining_days} days</strong>
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="col-12"><p class="mb-0 text-warning">No leave balances found for current year.</p></div>';
                }
            })
            .catch(error => {
                console.error('Error loading leave balances:', error);
                document.getElementById('leaveBalances').innerHTML = '<div class="col-12"><p class="mb-0 text-danger">Error loading leave balances.</p></div>';
            });
    }
    
    function calculateLeaveDays() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        const isHalfDay = halfDayCheck.checked;
        
        if (startDate && endDate) {
            const data = {
                start_date: startDate,
                end_date: endDate,
                is_half_day: isHalfDay
            };
            
            fetch('{% url "core:calculate_leave_days" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.total_days !== undefined) {
                    dateCalculation.textContent = `Total leave days: ${data.total_days} ${data.total_days === 1 ? 'day' : 'days'}`;
                    dateInfo.style.display = 'block';
                } else {
                    dateInfo.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error calculating leave days:', error);
                dateInfo.style.display = 'none';
            });
        } else {
            dateInfo.style.display = 'none';
        }
    }

    function loadHolidays() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;

        if (startDate && endDate) {
            fetch(`{% url "core:get_holidays" %}?start_date=${startDate}&end_date=${endDate}`)
                .then(response => response.json())
                .then(data => {
                    const holidayInfo = document.getElementById('holidayInfo');
                    const holidayList = document.getElementById('holidayList');

                    if (data.success && data.holidays.length > 0) {
                        holidayInfo.style.display = 'block';
                        holidayList.innerHTML = data.holidays.map(holiday => `
                            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                                <div>
                                    <strong>${holiday.name}</strong>
                                    <small class="text-muted d-block">${holiday.formatted_date} (${holiday.day_name})</small>
                                    ${holiday.description ? `<small class="text-muted">${holiday.description}</small>` : ''}
                                </div>
                                <span class="badge ${holiday.is_optional ? 'bg-warning' : 'bg-success'} text-dark">
                                    ${holiday.is_optional ? 'Optional' : 'Mandatory'}
                                </span>
                            </div>
                        `).join('');
                    } else {
                        holidayInfo.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading holidays:', error);
                    document.getElementById('holidayInfo').style.display = 'none';
                });
        } else {
            document.getElementById('holidayInfo').style.display = 'none';
        }
    }
});
</script>
{% endblock %}
