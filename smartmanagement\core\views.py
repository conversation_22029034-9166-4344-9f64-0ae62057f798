from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import date, timedelta, datetime
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

from .models import (
    Employee, Department, Designation, LeaveType,
    HolidayCalendar, LeaveBalance, LeaveRequest, Attendance
)

# Dashboard Views
@login_required
def leave_attendance_dashboard(request):
    """Main dashboard for Leave & Attendance Management"""
    try:
        employee = request.user.employee_profile
    except:
        # If user doesn't have employee profile, redirect to create one
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    # Get current year data
    current_year = date.today().year
    current_month = date.today().month

    # Leave Statistics
    leave_balances = LeaveBalance.objects.filter(employee=employee, year=current_year)
    total_leave_balance = sum([lb.remaining_days for lb in leave_balances])

    # Recent leave requests
    recent_leaves = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')[:5]

    # Attendance Statistics for current month
    attendance_records = Attendance.objects.filter(
        employee=employee,
        date__year=current_year,
        date__month=current_month
    )

    present_days = attendance_records.filter(status='Present').count()
    absent_days = attendance_records.filter(status='Absent').count()
    leave_days = attendance_records.filter(status='Leave').count()
    wfh_days = attendance_records.filter(status='WFH').count()

    # Working hours this month
    total_working_hours = attendance_records.aggregate(
        total=Sum('working_hours')
    )['total'] or 0

    # Upcoming holidays
    upcoming_holidays = HolidayCalendar.objects.filter(
        date__gte=date.today()
    ).order_by('date')[:5]

    context = {
        'employee': employee,
        'total_leave_balance': total_leave_balance,
        'leave_balances': leave_balances,
        'recent_leaves': recent_leaves,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'wfh_days': wfh_days,
        'total_working_hours': total_working_hours,
        'upcoming_holidays': upcoming_holidays,
        'current_month': date.today().strftime('%B %Y'),
    }

    return render(request, 'core/dashboard.html', context)

@login_required
def admin_leave_dashboard(request):
    """Admin dashboard for Leave & Attendance Management"""
    if request.user.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('leave_attendance_dashboard')

    # Get statistics
    total_employees = Employee.objects.filter(status='Active').count()
    pending_leaves = LeaveRequest.objects.filter(status='Pending').count()

    # Today's attendance
    today = date.today()
    today_attendance = Attendance.objects.filter(date=today)
    present_today = today_attendance.filter(status='Present').count()
    absent_today = today_attendance.filter(status='Absent').count()

    # Recent leave requests
    recent_leave_requests = LeaveRequest.objects.select_related(
        'employee__user', 'leave_type'
    ).order_by('-applied_on')[:10]

    # Department wise statistics
    departments = Department.objects.annotate(
        employee_count=Count('employee', filter=Q(employee__status='Active'))
    )

    context = {
        'total_employees': total_employees,
        'pending_leaves': pending_leaves,
        'present_today': present_today,
        'absent_today': absent_today,
        'recent_leave_requests': recent_leave_requests,
        'departments': departments,
    }

    return render(request, 'core/admin_dashboard.html', context)

# Employee Profile Management
@login_required
def create_employee_profile(request):
    """Create employee profile for users who don't have one"""
    try:
        # Check if user already has an employee profile
        employee = request.user.employee_profile
        messages.info(request, 'You already have an employee profile.')
        return redirect('core:dashboard')
    except:
        pass

    if request.method == 'POST':
        from .forms import EmployeeForm
        form = EmployeeForm(request.POST)
        if form.is_valid():
            employee = form.save(commit=False)
            employee.user = request.user
            employee.save()
            messages.success(request, 'Employee profile created successfully!')
            return redirect('core:dashboard')
    else:
        from .forms import EmployeeForm
        form = EmployeeForm()

    return render(request, 'core/employee_profile_form.html', {'form': form})

# API Views for AJAX calls
@login_required
def check_leave_balance(request):
    """API endpoint to check leave balances"""
    try:
        employee = request.user.employee_profile
        current_year = date.today().year
        balances = LeaveBalance.objects.filter(employee=employee, year=current_year)

        balance_data = []
        for balance in balances:
            balance_data.append({
                'leave_type': balance.leave_type.name,
                'total_allocated': balance.total_allocated,
                'used_days': float(balance.used_days),
                'remaining_days': balance.remaining_days
            })

        return JsonResponse({'balances': balance_data})
    except:
        return JsonResponse({'balances': []})

@login_required
@require_POST
def calculate_leave_days(request):
    """API endpoint to calculate leave days"""
    try:
        data = json.loads(request.body)
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        is_half_day = data.get('is_half_day', False)

        if is_half_day:
            total_days = 0.5
        else:
            # Simple calculation - can be enhanced to exclude weekends/holidays
            total_days = (end_date - start_date).days + 1

            # Exclude weekends
            current_date = start_date
            weekend_days = 0
            while current_date <= end_date:
                if current_date.weekday() >= 5:  # Saturday=5, Sunday=6
                    weekend_days += 1
                current_date += timedelta(days=1)

            total_days -= weekend_days

            # Exclude holidays
            holidays = HolidayCalendar.objects.filter(
                date__range=[start_date, end_date]
            ).count()
            total_days -= holidays

            total_days = max(0, total_days)

        return JsonResponse({'total_days': total_days})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

# Leave Request Views
@login_required
def leave_request_create(request):
    """Create a new leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.method == 'POST':
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(request.POST, employee=employee)
        if form.is_valid():
            leave_request = form.save(commit=False)
            leave_request.employee = employee
            leave_request.save()
            messages.success(request, 'Leave request submitted successfully!')
            return redirect('core:leave_request_list')
    else:
        from .forms import LeaveRequestForm
        form = LeaveRequestForm(employee=employee)

    return render(request, 'core/leave_request_form.html', {'form': form})

@login_required
def leave_request_list(request):
    """List all leave requests for the current user or all if admin"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.user.role == 'admin':
        leave_requests = LeaveRequest.objects.select_related('employee__user', 'leave_type').order_by('-applied_on')
    else:
        leave_requests = LeaveRequest.objects.filter(employee=employee).order_by('-applied_on')

    # Pagination
    paginator = Paginator(leave_requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'is_admin': request.user.role == 'admin'
    }

    return render(request, 'core/leave_request_list.html', context)

# Placeholder views for other functionalities
@login_required
def employee_list(request):
    return render(request, 'core/employee_list.html')

@login_required
def employee_create(request):
    context = {'page_title': 'Add Employee', 'page_description': 'Create new employee profile'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_detail(request, pk):
    context = {'page_title': 'Employee Details', 'page_description': 'View employee information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_edit(request, pk):
    context = {'page_title': 'Edit Employee', 'page_description': 'Update employee information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_delete(request, pk):
    return redirect('core:employee_list')

@login_required
def department_list(request):
    return render(request, 'core/department_list.html')

@login_required
def department_create(request):
    context = {'page_title': 'Add Department', 'page_description': 'Create new department'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def department_edit(request, pk):
    context = {'page_title': 'Edit Department', 'page_description': 'Update department information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def department_delete(request, pk):
    return redirect('core:department_list')

@login_required
def designation_list(request):
    context = {'page_title': 'Designation Management', 'page_description': 'Manage job positions and roles'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def designation_create(request):
    context = {'page_title': 'Add Designation', 'page_description': 'Create new job position'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def designation_edit(request, pk):
    context = {'page_title': 'Edit Designation', 'page_description': 'Update job position information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def designation_delete(request, pk):
    return redirect('core:designation_list')

@login_required
def leave_type_list(request):
    context = {'page_title': 'Leave Type Management', 'page_description': 'Manage different types of leaves'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_type_create(request):
    context = {'page_title': 'Add Leave Type', 'page_description': 'Create new leave type'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_type_edit(request, pk):
    context = {'page_title': 'Edit Leave Type', 'page_description': 'Update leave type information'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_type_delete(request, pk):
    return redirect('core:leave_type_list')

@login_required
def holiday_list(request):
    """List all holidays with filtering and pagination"""
    # Get current year or year from query parameter
    current_year = date.today().year
    year = request.GET.get('year', current_year)
    try:
        year = int(year)
    except (ValueError, TypeError):
        year = current_year

    # Get holidays for the selected year
    holidays = HolidayCalendar.objects.filter(date__year=year).order_by('date')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        holidays = holidays.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by optional/mandatory
    holiday_type = request.GET.get('type', '')
    if holiday_type == 'optional':
        holidays = holidays.filter(is_optional=True)
    elif holiday_type == 'mandatory':
        holidays = holidays.filter(is_optional=False)

    # Pagination
    paginator = Paginator(holidays, 12)  # 12 holidays per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get available years for filter
    available_years = HolidayCalendar.objects.dates('date', 'year').values_list('date__year', flat=True)
    available_years = sorted(set(available_years), reverse=True)

    # Get upcoming holidays for quick view
    upcoming_holidays = HolidayCalendar.objects.filter(
        date__gte=date.today()
    ).order_by('date')[:5]

    context = {
        'page_obj': page_obj,
        'current_year': year,
        'available_years': available_years,
        'search_query': search_query,
        'holiday_type': holiday_type,
        'upcoming_holidays': upcoming_holidays,
        'is_admin': request.user.role == 'admin',
        'total_holidays': holidays.count(),
    }
    return render(request, 'core/holiday_list.html', context)

@login_required
def holiday_create(request):
    """Create a new holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to add holidays.')
        return redirect('core:holiday_list')

    if request.method == 'POST':
        from .forms import HolidayForm
        form = HolidayForm(request.POST)
        if form.is_valid():
            try:
                holiday = form.save()
                messages.success(request, f'Holiday "{holiday.name}" added successfully!')
                return redirect('core:holiday_list')
            except Exception as e:
                messages.error(request, f'Error adding holiday: {str(e)}')
    else:
        from .forms import HolidayForm
        form = HolidayForm()
        # Set minimum date to today
        form.fields['date'].widget.attrs['min'] = date.today().isoformat()

    context = {
        'form': form,
        'is_edit': False,
    }
    return render(request, 'core/holiday_form.html', context)

@login_required
def holiday_edit(request, pk):
    """Edit an existing holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to edit holidays.')
        return redirect('core:holiday_list')

    try:
        holiday = get_object_or_404(HolidayCalendar, pk=pk)

        if request.method == 'POST':
            from .forms import HolidayForm
            form = HolidayForm(request.POST, instance=holiday)
            if form.is_valid():
                try:
                    updated_holiday = form.save()
                    messages.success(request, f'Holiday "{updated_holiday.name}" updated successfully!')
                    return redirect('core:holiday_list')
                except Exception as e:
                    messages.error(request, f'Error updating holiday: {str(e)}')
        else:
            from .forms import HolidayForm
            form = HolidayForm(instance=holiday)

        context = {
            'form': form,
            'holiday': holiday,
            'is_edit': True,
        }
        return render(request, 'core/holiday_form.html', context)

    except Exception as e:
        messages.error(request, f'Error loading holiday: {str(e)}')
        return redirect('core:holiday_list')

@login_required
def holiday_delete(request, pk):
    """Delete a holiday (admin only)"""
    # Check if user is admin
    if request.user.role != 'admin':
        messages.error(request, 'You do not have permission to delete holidays.')
        return redirect('core:holiday_list')

    try:
        holiday = get_object_or_404(HolidayCalendar, pk=pk)
        holiday_name = holiday.name
        holiday.delete()
        messages.success(request, f'Holiday "{holiday_name}" deleted successfully!')
    except Exception as e:
        messages.error(request, f'Error deleting holiday: {str(e)}')

    return redirect('core:holiday_list')

@login_required
def leave_request_detail(request, pk):
    """View detailed information about a leave request"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request
        if request.user.role == 'admin':
            # Admin can view any leave request
            leave_request = get_object_or_404(LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user'), pk=pk)
        else:
            # Regular users can only view their own leave requests
            leave_request = get_object_or_404(LeaveRequest.objects.select_related('employee__user', 'leave_type', 'approved_by__user'), pk=pk, employee=employee)

        context = {
            'leave_request': leave_request,
            'is_admin': request.user.role == 'admin',
            'can_edit': leave_request.status == 'Pending' and leave_request.employee == employee,
            'can_cancel': leave_request.status == 'Pending' and leave_request.employee == employee,
            'can_approve': request.user.role == 'admin' and leave_request.status == 'Pending',
        }
        return render(request, 'core/leave_request_detail.html', context)

    except Exception as e:
        messages.error(request, f'Error loading leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_edit(request, pk):
    """Edit a leave request (only if pending and belongs to current user)"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request - only allow editing own requests
        leave_request = get_object_or_404(LeaveRequest.objects.select_related('leave_type'), pk=pk, employee=employee)

        # Only allow editing pending requests
        if leave_request.status != 'Pending':
            messages.error(request, 'You can only edit pending leave requests.')
            return redirect('core:leave_request_detail', pk=pk)

        if request.method == 'POST':
            from .forms import LeaveRequestForm
            form = LeaveRequestForm(request.POST, instance=leave_request, employee=employee)
            if form.is_valid():
                updated_request = form.save(commit=False)
                updated_request.employee = employee
                updated_request.save()
                messages.success(request, 'Leave request updated successfully!')
                return redirect('core:leave_request_detail', pk=pk)
        else:
            from .forms import LeaveRequestForm
            form = LeaveRequestForm(instance=leave_request, employee=employee)

        context = {
            'form': form,
            'leave_request': leave_request,
            'is_edit': True,
        }
        return render(request, 'core/leave_request_edit.html', context)

    except Exception as e:
        messages.error(request, f'Error loading leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_cancel(request, pk):
    """Cancel a leave request (only if pending and belongs to current user)"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    try:
        # Get the leave request - only allow cancelling own requests
        leave_request = get_object_or_404(LeaveRequest, pk=pk, employee=employee)

        # Only allow cancelling pending requests
        if leave_request.status != 'Pending':
            messages.error(request, 'You can only cancel pending leave requests.')
            return redirect('core:leave_request_detail', pk=pk)

        # Update status to cancelled
        leave_request.status = 'Cancelled'
        leave_request.save()

        messages.success(request, 'Leave request cancelled successfully!')
        return redirect('core:leave_request_list')

    except Exception as e:
        messages.error(request, f'Error cancelling leave request: {str(e)}')
        return redirect('core:leave_request_list')

@login_required
def leave_request_approve(request, pk):
    context = {'page_title': 'Approve Leave Request', 'page_description': 'Approve or reject leave request'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_balance_list(request):
    context = {'page_title': 'Leave Balance Management', 'page_description': 'Manage employee leave balances'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_balance_create(request):
    context = {'page_title': 'Add Leave Balance', 'page_description': 'Create leave balance for employee'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def leave_balance_edit(request, pk):
    context = {'page_title': 'Edit Leave Balance', 'page_description': 'Update employee leave balance'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_list(request):
    """List attendance records for the current user or all if admin"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.user.role == 'admin':
        attendance_records = Attendance.objects.select_related('employee__user').order_by('-date')
    else:
        attendance_records = Attendance.objects.filter(employee=employee).order_by('-date')

    # Get filter parameters
    month = request.GET.get('month')
    year = request.GET.get('year')
    status = request.GET.get('status')

    # Apply filters
    if month:
        attendance_records = attendance_records.filter(date__month=month)
    if year:
        attendance_records = attendance_records.filter(date__year=year)
    if status:
        attendance_records = attendance_records.filter(status=status)

    # Pagination
    paginator = Paginator(attendance_records, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate monthly stats
    current_month = date.today().month
    current_year = date.today().year

    monthly_records = Attendance.objects.filter(
        employee=employee,
        date__month=current_month,
        date__year=current_year
    )

    present_days = monthly_records.filter(status='Present').count()
    absent_days = monthly_records.filter(status='Absent').count()
    leave_days = monthly_records.filter(status='Leave').count()
    wfh_days = monthly_records.filter(status='WFH').count()
    total_hours = monthly_records.aggregate(total=Sum('working_hours'))['total'] or 0

    context = {
        'page_obj': page_obj,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'wfh_days': wfh_days,
        'total_hours': total_hours,
        'is_admin': request.user.role == 'admin',
        'current_month': date.today().strftime('%B %Y'),
    }

    return render(request, 'core/attendance_list.html', context)

@login_required
def mark_attendance(request):
    """Mark attendance for the current user"""
    try:
        employee = request.user.employee_profile
    except:
        messages.warning(request, 'Please complete your employee profile first.')
        return redirect('create_employee_profile')

    if request.method == 'POST':
        from .forms import AttendanceForm
        form = AttendanceForm(request.POST)
        if form.is_valid():
            attendance = form.save(commit=False)
            attendance.employee = employee

            # Get client IP address
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                attendance.ip_address = x_forwarded_for.split(',')[0]
            else:
                attendance.ip_address = request.META.get('REMOTE_ADDR')

            attendance.save()
            messages.success(request, 'Attendance marked successfully!')
            return redirect('core:attendance_list')
    else:
        from .forms import AttendanceForm
        # Check if attendance already exists for today
        today = date.today()
        existing_attendance = Attendance.objects.filter(
            employee=employee,
            date=today
        ).first()

        if existing_attendance:
            form = AttendanceForm(instance=existing_attendance)
            messages.info(request, 'Attendance already marked for today. You can update it below.')
        else:
            form = AttendanceForm(initial={'date': today})

    context = {
        'form': form,
        'today': date.today().isoformat(),
    }

    return render(request, 'core/attendance_form.html', context)

@login_required
def attendance_edit(request, pk):
    context = {'page_title': 'Edit Attendance', 'page_description': 'Update attendance record'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_report(request):
    context = {'page_title': 'Attendance Report', 'page_description': 'Generate detailed attendance reports'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_bulk_upload(request):
    context = {'page_title': 'Bulk Upload Attendance', 'page_description': 'Upload attendance data in bulk'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_summary(request):
    return JsonResponse({'status': 'success'})

@login_required
def get_holidays(request):
    """API endpoint to get holidays for a date range"""
    try:
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        year = request.GET.get('year')

        if year:
            holidays = HolidayCalendar.objects.filter(date__year=year)
        elif start_date and end_date:
            holidays = HolidayCalendar.objects.filter(
                date__range=[start_date, end_date]
            )
        else:
            # Default to current year
            holidays = HolidayCalendar.objects.filter(date__year=date.today().year)

        holidays_data = []
        for holiday in holidays:
            holidays_data.append({
                'date': holiday.date.isoformat(),
                'name': holiday.name,
                'description': holiday.description or '',
                'is_optional': holiday.is_optional,
                'formatted_date': holiday.date.strftime('%B %d, %Y'),
                'day_name': holiday.date.strftime('%A'),
            })

        return JsonResponse({
            'success': True,
            'holidays': holidays_data,
            'count': len(holidays_data)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)

@login_required
def reports_dashboard(request):
    return render(request, 'core/reports_dashboard.html')

@login_required
def leave_summary_report(request):
    context = {'page_title': 'Leave Summary Report', 'page_description': 'Generate leave utilization reports'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def attendance_summary_report(request):
    context = {'page_title': 'Attendance Summary Report', 'page_description': 'Generate attendance summary reports'}
    return render(request, 'core/placeholder_template.html', context)

@login_required
def employee_wise_report(request):
    context = {'page_title': 'Employee Wise Report', 'page_description': 'Generate individual employee reports'}
    return render(request, 'core/placeholder_template.html', context)