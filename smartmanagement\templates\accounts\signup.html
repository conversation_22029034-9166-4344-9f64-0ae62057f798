{% extends 'base.html' %}

{% block title %}Sign Up - Smart Management System{% endblock %}

{% block content %}
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="card animate-on-scroll">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                            <h2 class="card-title">Create Account</h2>
                            <p class="text-muted">Join us and start managing smartly</p>
                        </div>

                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" id="signupForm">
                            {% csrf_token %}

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>First Name
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="{{ form.first_name.id_for_label }}"
                                           name="{{ form.first_name.name }}"
                                           placeholder="Enter first name"
                                           required>
                                    {% if form.first_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.first_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>Last Name
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="{{ form.last_name.id_for_label }}"
                                           name="{{ form.last_name.name }}"
                                           placeholder="Enter last name"
                                           required>
                                    {% if form.last_name.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.last_name.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <i class="fas fa-at me-2"></i>Username
                                </label>
                                <input type="text"
                                       class="form-control"
                                       id="{{ form.username.id_for_label }}"
                                       name="{{ form.username.name }}"
                                       placeholder="Choose a username"
                                       required>
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    <i class="fas fa-envelope me-2"></i>Email Address
                                </label>
                                <input type="email"
                                       class="form-control"
                                       id="{{ form.email.id_for_label }}"
                                       name="{{ form.email.name }}"
                                       placeholder="Enter your email"
                                       required>
                                {% if form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <div class="input-group">
                                    <input type="password"
                                           class="form-control"
                                           id="{{ form.password.id_for_label }}"
                                           name="{{ form.password.name }}"
                                           placeholder="Create a strong password"
                                           required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ form.password.id_for_label }}', 'toggleIcon1')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.role.id_for_label }}" class="form-label">
                                    <i class="fas fa-user-tag me-2"></i>Role
                                </label>
                                <select class="form-control"
                                        id="{{ form.role.id_for_label }}"
                                        name="{{ form.role.name }}"
                                        required>
                                    <option value="">Select your role</option>
                                    <option value="admin">Admin</option>
                                    <option value="employee">Employee</option>
                                </select>
                                {% if form.role.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.role.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    I agree to the <a href="#" class="text-primary">Terms and Conditions</a>
                                </label>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>

                            <div class="text-center">
                                <p class="mb-0">Already have an account?
                                    <a href="{% url 'login' %}" class="text-primary text-decoration-none">
                                        <strong>Sign in here</strong>
                                    </a>
                                </p>
                            </div>
                        </form>

                        <div class="loading">
                            <div class="spinner"></div>
                            <p class="mt-2">Creating your account...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function togglePassword(fieldId, iconId) {
        const passwordField = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(iconId);

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    document.getElementById('signupForm').addEventListener('submit', function(e) {
        showLoading('signupForm');
    });

    // Password strength indicator
    document.getElementById('{{ form.password.id_for_label }}').addEventListener('input', function() {
        const password = this.value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('strengthText');

        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;

        const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];

        if (strengthBar && strengthText) {
            strengthBar.style.width = (strength * 20) + '%';
            strengthBar.style.backgroundColor = strengthColors[strength - 1] || '#e5e7eb';
            strengthText.textContent = strengthLevels[strength - 1] || '';
            strengthText.style.color = strengthColors[strength - 1] || '#6b7280';
        }
    });
</script>
{% endblock %}
