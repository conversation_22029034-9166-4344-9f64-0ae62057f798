{% extends 'base.html' %}

{% block title %}Create Employee Profile - Smart Management System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Create Employee Profile</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">Please complete your employee profile to access the Leave & Attendance Management System.</p>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.employee_code.id_for_label }}" class="form-label">Employee Code *</label>
                                    {{ form.employee_code }}
                                    {% if form.employee_code.errors %}
                                        <div class="text-danger">{{ form.employee_code.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.date_of_joining.id_for_label }}" class="form-label">Date of Joining *</label>
                                    {{ form.date_of_joining }}
                                    {% if form.date_of_joining.errors %}
                                        <div class="text-danger">{{ form.date_of_joining.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.department.id_for_label }}" class="form-label">Department</label>
                                    {{ form.department }}
                                    {% if form.department.errors %}
                                        <div class="text-danger">{{ form.department.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.designation.id_for_label }}" class="form-label">Designation</label>
                                    {{ form.designation }}
                                    {% if form.designation.errors %}
                                        <div class="text-danger">{{ form.designation.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.manager.id_for_label }}" class="form-label">Manager</label>
                                    {{ form.manager }}
                                    {% if form.manager.errors %}
                                        <div class="text-danger">{{ form.manager.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary me-3">
                                <i class="fas fa-save me-2"></i>Create Profile
                            </button>
                            <a href="{% url 'home' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
