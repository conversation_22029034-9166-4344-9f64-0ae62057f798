{% extends 'base.html' %}

{% block title %}Leave Request Details - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .detail-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
        color: #2d3436;
    }

    .status-approved {
        background: linear-gradient(135deg, #55efc4, #00b894);
        color: #2d3436;
    }

    .status-rejected {
        background: linear-gradient(135deg, #fd79a8, #e84393);
        color: white;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #b2bec3, #636e72);
        color: white;
    }

    .info-item {
        padding: 15px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .info-value {
        color: #636e72;
        font-size: 1.1rem;
    }

    .action-buttons {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-eye me-3"></i>Leave Request Details
                        </h1>
                        <p class="lead mb-0">View detailed information about the leave request</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_request_list' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <!-- Main Details Card -->
            <div class="detail-card">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-alt me-2 text-primary"></i>
                            {{ leave_request.leave_type.name }}
                        </h4>
                        <span class="status-badge status-{{ leave_request.status|lower }}">
                            {{ leave_request.status }}
                        </span>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-user me-2"></i>Employee
                                </div>
                                <div class="info-value">
                                    {{ leave_request.employee.full_name }}
                                    {% if is_admin %}
                                        <small class="text-muted">({{ leave_request.employee.employee_code }})</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar me-2"></i>Applied On
                                </div>
                                <div class="info-value">
                                    {{ leave_request.applied_on|date:"M d, Y" }}
                                    <small class="text-muted">at {{ leave_request.applied_on|time:"g:i A" }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-day me-2"></i>Start Date
                                </div>
                                <div class="info-value">
                                    {{ leave_request.start_date|date:"M d, Y" }}
                                    {% if leave_request.is_half_day %}
                                        <span class="badge bg-info ms-2">{{ leave_request.half_day_period }} Half Day</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-calendar-check me-2"></i>End Date
                                </div>
                                <div class="info-value">
                                    {{ leave_request.end_date|date:"M d, Y" }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-clock me-2"></i>Total Duration
                        </div>
                        <div class="info-value">
                            {{ leave_request.total_days }} day{{ leave_request.total_days|floatformat:0|pluralize }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-comment me-2"></i>Reason
                        </div>
                        <div class="info-value">
                            {{ leave_request.reason|linebreaks }}
                        </div>
                    </div>

                    {% if leave_request.emergency_contact %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-phone me-2"></i>Emergency Contact
                        </div>
                        <div class="info-value">
                            {{ leave_request.emergency_contact }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Status Information Card -->
            <div class="detail-card">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-info"></i>Status Information
                    </h5>
                </div>
                <div class="card-body">
                    {% if leave_request.status == 'Approved' and leave_request.approved_by %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-user-check me-2"></i>Approved By
                        </div>
                        <div class="info-value">
                            {{ leave_request.approved_by.full_name }}
                        </div>
                    </div>

                    {% if leave_request.approved_on %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-calendar-check me-2"></i>Approved On
                        </div>
                        <div class="info-value">
                            {{ leave_request.approved_on|date:"M d, Y" }}
                            <small class="text-muted">at {{ leave_request.approved_on|time:"g:i A" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% elif leave_request.status == 'Rejected' %}
                    {% if leave_request.approved_by %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-user-times me-2"></i>Rejected By
                        </div>
                        <div class="info-value">
                            {{ leave_request.approved_by.full_name }}
                        </div>
                    </div>
                    {% endif %}

                    {% if leave_request.approved_on %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-calendar-times me-2"></i>Rejected On
                        </div>
                        <div class="info-value">
                            {{ leave_request.approved_on|date:"M d, Y" }}
                            <small class="text-muted">at {{ leave_request.approved_on|time:"g:i A" }}</small>
                        </div>
                    </div>
                    {% endif %}

                    {% if leave_request.rejection_reason %}
                    <div class="info-item">
                        <div class="info-label">
                            <i class="fas fa-comment-slash me-2"></i>Rejection Reason
                        </div>
                        <div class="info-value">
                            {{ leave_request.rejection_reason|linebreaks }}
                        </div>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-hourglass-half fa-2x text-warning mb-2"></i>
                        <p class="mb-0 text-muted">Request is pending approval</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <h6 class="mb-3">
                    <i class="fas fa-cogs me-2"></i>Actions
                </h6>

                <div class="d-grid gap-2">
                    {% if can_edit %}
                    <a href="{% url 'core:leave_request_edit' leave_request.pk %}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Request
                    </a>
                    {% endif %}

                    {% if can_cancel %}
                    <a href="{% url 'core:leave_request_cancel' leave_request.pk %}"
                       class="btn btn-danger"
                       onclick="return confirm('Are you sure you want to cancel this leave request?')">
                        <i class="fas fa-times me-2"></i>Cancel Request
                    </a>
                    {% endif %}

                    {% if can_approve %}
                    <a href="{% url 'core:leave_request_approve' leave_request.pk %}" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Approve/Reject
                    </a>
                    {% endif %}

                    <a href="{% url 'core:leave_request_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
