from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout, authenticate
from .forms import UserRegistrationForm, UserLoginForm, UserEditForm, UserCreateForm
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import CustomUser
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

def home(request):
    return render(request, 'accounts/home.html')

def signup_view(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.set_password(form.cleaned_data['password'])
            user.save()
            return redirect('login')
    else:
        form = UserRegistrationForm()
    return render(request, 'accounts/signup.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        form = UserLoginForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            login(request, user)
            if user.role == 'admin':
                return redirect('admin_dashboard')
            else:
                return redirect('employee_dashboard')
    else:
        form = UserLoginForm()
    return render(request, 'accounts/login.html', {'form': form})

@login_required
def admin_dashboard(request):
    # Get user statistics
    total_users = CustomUser.objects.filter(is_deleted=False).count()
    admin_users = CustomUser.objects.filter(role='admin', is_deleted=False).count()
    employee_users = CustomUser.objects.filter(role='employee', is_deleted=False).count()
    active_users = CustomUser.objects.filter(is_active=True, is_deleted=False).count()
    inactive_users = CustomUser.objects.filter(is_active=False, is_deleted=False).count()
    deleted_users = CustomUser.objects.filter(is_deleted=True).count()

    # Get new users today
    from django.utils import timezone
    today = timezone.now().date()
    new_users_today = CustomUser.objects.filter(created_at__date=today).count()

    # Calculate percentages
    admin_percentage = (admin_users / total_users * 100) if total_users > 0 else 0
    employee_percentage = (employee_users / total_users * 100) if total_users > 0 else 0

    # Get recent users (last 5)
    recent_users = CustomUser.objects.filter(is_deleted=False).order_by('-created_at')[:5]

    context = {
        'total_users': total_users,
        'admin_users': admin_users,
        'employee_users': employee_users,
        'active_users': active_users,
        'inactive_users': inactive_users,
        'deleted_users': deleted_users,
        'new_users_today': new_users_today,
        'admin_percentage': round(admin_percentage, 1),
        'employee_percentage': round(employee_percentage, 1),
        'recent_users': recent_users,
    }

    return render(request, 'accounts/admin_dashboard.html', context)

@login_required
def employee_dashboard(request):
    # Calculate profile completion percentage
    user = request.user
    total_fields = 7  # first_name, last_name, email, phone, address, date_of_birth, username
    completed_fields = 0

    if user.first_name:
        completed_fields += 1
    if user.last_name:
        completed_fields += 1
    if user.email:
        completed_fields += 1
    if user.phone:
        completed_fields += 1
    if user.address:
        completed_fields += 1
    if user.date_of_birth:
        completed_fields += 1
    if user.username:
        completed_fields += 1

    profile_completion = round((completed_fields / total_fields) * 100)

    context = {
        'profile_completion': profile_completion,
    }

    return render(request, 'accounts/employee_dashboard.html', context)

def logout_view(request):
    logout(request)
    return redirect('login')

# Admin-only decorator
def admin_required(view_func):
    def wrapper(request, *args, **kwargs):
        if not request.user.is_authenticated or request.user.role != 'admin':
            messages.error(request, 'You need admin privileges to access this page.')
            return redirect('home')
        return view_func(request, *args, **kwargs)
    return wrapper

@login_required
@admin_required
def user_list(request):
    """Display all users with search and filter functionality"""
    search_query = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    status_filter = request.GET.get('status', '')

    users = CustomUser.objects.all()

    # Apply filters
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    if role_filter:
        users = users.filter(role=role_filter)

    if status_filter == 'active':
        users = users.filter(is_active=True, is_deleted=False)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'deleted':
        users = users.filter(is_deleted=True)
    else:
        users = users.filter(is_deleted=False)  # Default: show only non-deleted users

    # Pagination
    paginator = Paginator(users, 10)  # Show 10 users per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'status_filter': status_filter,
        'total_users': users.count(),
    }

    return render(request, 'accounts/user_list.html', context)

@login_required
@admin_required
def user_detail(request, user_id):
    """Display detailed information about a specific user"""
    user = get_object_or_404(CustomUser, id=user_id)

    context = {
        'user_detail': user,
    }

    return render(request, 'accounts/user_detail.html', context)

@login_required
@admin_required
def user_edit(request, user_id):
    """Edit user information"""
    user = get_object_or_404(CustomUser, id=user_id)

    if request.method == 'POST':
        form = UserEditForm(request.POST, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, f'User {user.username} updated successfully!')
            return redirect('user_detail', user_id=user.id)
    else:
        form = UserEditForm(instance=user)

    context = {
        'form': form,
        'user_detail': user,
    }

    return render(request, 'accounts/user_edit.html', context)

@login_required
@admin_required
def user_create(request):
    """Create a new user"""
    if request.method == 'POST':
        form = UserCreateForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.set_password(form.cleaned_data['password'])
            user.save()
            messages.success(request, f'User {user.username} created successfully!')
            return redirect('user_list')
    else:
        form = UserCreateForm()

    context = {
        'form': form,
    }

    return render(request, 'accounts/user_create.html', context)

@login_required
@admin_required
@require_POST
def user_soft_delete(request, user_id):
    """Soft delete a user"""
    user = get_object_or_404(CustomUser, id=user_id)

    if user == request.user:
        return JsonResponse({'success': False, 'message': 'You cannot delete yourself!'})

    user.soft_delete()
    messages.success(request, f'User {user.username} has been deleted successfully!')

    return JsonResponse({'success': True, 'message': 'User deleted successfully!'})

@login_required
@admin_required
@require_POST
def user_restore(request, user_id):
    """Restore a soft deleted user"""
    user = get_object_or_404(CustomUser, id=user_id)

    user.restore()
    messages.success(request, f'User {user.username} has been restored successfully!')

    return JsonResponse({'success': True, 'message': 'User restored successfully!'})

@login_required
@admin_required
@require_POST
def user_toggle_status(request, user_id):
    """Toggle user active status"""
    user = get_object_or_404(CustomUser, id=user_id)

    if user == request.user:
        return JsonResponse({'success': False, 'message': 'You cannot deactivate yourself!'})

    user.is_active = not user.is_active
    user.save()

    status = 'activated' if user.is_active else 'deactivated'
    messages.success(request, f'User {user.username} has been {status}!')

    return JsonResponse({'success': True, 'message': f'User {status} successfully!'})
