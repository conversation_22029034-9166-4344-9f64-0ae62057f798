{% extends 'base.html' %}

{% block title %}Admin Dashboard - Smart Management System{% endblock %}

{% block content %}
<!-- Welcome Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        Welcome back, {{ user.first_name|default:user.username }}!
                    </h1>
                    <p class="lead mb-0">Admin Dashboard - Manage your system efficiently</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- User Statistics Cards -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll">
                    <div class="stat-number">
                        <i class="fas fa-users"></i>
                        <div>{{ total_users|default:"0" }}</div>
                    </div>
                    <div class="stat-label">Total Users</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user-shield"></i>
                        <div>{{ admin_users|default:"0" }}</div>
                    </div>
                    <div class="stat-label">Admin Users</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user-tie"></i>
                        <div>{{ employee_users|default:"0" }}</div>
                    </div>
                    <div class="stat-label">Employee Users</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user-check"></i>
                        <div>{{ active_users|default:"0" }}</div>
                    </div>
                    <div class="stat-label">Active Users</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-users-cog text-primary me-2"></i>
                        User Management
                    </h4>
                    <div class="d-grid gap-2">
                        <a href="{% url 'user_list' %}" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>View All Users
                        </a>
                        <a href="{% url 'user_create' %}" class="btn btn-success">
                            <i class="fas fa-user-plus me-2"></i>Add New User
                        </a>
                        <a href="{% url 'user_list' %}?status=active" class="btn btn-info">
                            <i class="fas fa-user-check me-2"></i>Active Users
                        </a>
                        <a href="{% url 'user_list' %}?status=deleted" class="btn btn-warning">
                            <i class="fas fa-user-slash me-2"></i>Deleted Users
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent User Activities -->
            <div class="col-lg-8 mb-4">
                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-history text-primary me-2"></i>
                        Recent User Activities
                    </h4>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                                {% if user.first_name %}
                                                    {{ user.first_name.0 }}{{ user.last_name.0|default:'' }}
                                                {% else %}
                                                    {{ user.username.0|upper }}
                                                {% endif %}
                                            </div>
                                            {{ user.first_name }} {{ user.last_name|default:user.username }}
                                        </div>
                                    </td>
                                    <td>User registered</td>
                                    <td>{{ user.created_at|timesince }} ago</td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-warning">Inactive</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No recent user activities</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{% url 'user_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>View All Users
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- User Role Distribution -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-pie text-primary me-2"></i>
                        User Role Distribution
                    </h4>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Admin Users</span>
                            <span>{{ admin_percentage|default:"0" }}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: {{ admin_percentage|default:"0" }}%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Employee Users</span>
                            <span>{{ employee_percentage|default:"0" }}%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: {{ employee_percentage|default:"0" }}%"></div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">Total: {{ total_users|default:"0" }} users</small>
                    </div>
                </div>
            </div>

            <!-- User Status Overview -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-users-cog text-success me-2"></i>
                        User Status Overview
                    </h4>
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h3 class="text-success">{{ active_users|default:"0" }}</h3>
                                <small class="text-muted">Active Users</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h3 class="text-warning">{{ inactive_users|default:"0" }}</h3>
                            <small class="text-muted">Inactive Users</small>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="text-danger">{{ deleted_users|default:"0" }}</h3>
                                <small class="text-muted">Deleted Users</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-info">{{ new_users_today|default:"0" }}</h3>
                            <small class="text-muted">New Today</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function showModal(modalId) {
        // Placeholder for modal functionality
        alert('Feature coming soon: ' + modalId);
    }

    function generateReport() {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
        btn.disabled = true;

        // Simulate report generation
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Report generated successfully!');
        }, 2000);
    }

    function showSystemSettings() {
        alert('System Settings panel coming soon!');
    }

    // Real-time updates simulation
    function updateStats() {
        const stats = document.querySelectorAll('.stat-number div');
        stats.forEach(stat => {
            if (!isNaN(parseInt(stat.textContent))) {
                const currentValue = parseInt(stat.textContent);
                const change = Math.floor(Math.random() * 10) - 5; // Random change between -5 and +5
                const newValue = Math.max(0, currentValue + change);
                stat.textContent = newValue;
            }
        });
    }

    // Update stats every 30 seconds
    setInterval(updateStats, 30000);

    // Animate progress bars on load
    window.addEventListener('load', function() {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.transition = 'width 1s ease-in-out';
                bar.style.width = width;
            }, 500);
        });
    });
</script>
{% endblock %}
